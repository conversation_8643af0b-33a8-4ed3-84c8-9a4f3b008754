/**
 * Verification script to check if sub-agents are properly configured with tools and function calling
 */

const { CreateDocumentationAgent } = require('./lib/agents/documentation/createDocumentationAgent');

async function verifySubAgentToolConfiguration() {
  console.log('🔍 Verifying Sub-Agent Tool Configuration');
  console.log('=' .repeat(60));

  try {
    // Create agent with sub-agents enabled
    const agent = new CreateDocumentationAgent({
      useSubAgents: true,
      maxSubAgents: 2,
      subAgentComplexity: 'complex', // Use complex to enable all tools
      streamResponse: true,
      onStreamUpdate: (update) => {
        console.log(`📊 [${update.stage}] ${update.message}`);
      }
    });

    console.log('✅ Agent created successfully');
    console.log(`   - Enhanced Processor: ${agent.enhancedProcessor ? 'Initialized' : 'Not Available'}`);
    console.log(`   - Sub-agents enabled: ${agent.options.useSubAgents}`);
    console.log(`   - Complexity level: ${agent.options.subAgentComplexity}`);
    console.log('');

    // Test tool determination for different specializations
    console.log('🛠️  Testing Tool Configuration for Different Specializations:');
    console.log('-' .repeat(60));

    const specializations = [
      'Technical Analysis',
      'Architecture Review', 
      'API Documentation',
      'Performance Analysis',
      'Security Analysis',
      'User Guide Creation'
    ];

    specializations.forEach(specialization => {
      const tools = agent._determineToolsForSpecialization(specialization, 'complex');
      const hasAnyTools = agent._hasAnyToolEnabled(tools);
      
      console.log(`📋 ${specialization}:`);
      console.log(`   - Chart Generation: ${tools.chartGeneration ? '✅' : '❌'}`);
      console.log(`   - Dashboard Creation: ${tools.dashboardCreation ? '✅' : '❌'}`);
      console.log(`   - Web Search: ${tools.webSearch ? '✅' : '❌'}`);
      console.log(`   - DateTime: ${tools.datetime ? '✅' : '❌'}`);
      console.log(`   - Question Answer: ${tools.questionAnswer ? '✅' : '❌'}`);
      console.log(`   - Any Tools Enabled: ${hasAnyTools ? '✅ YES' : '❌ NO'}`);
      console.log('');
    });

    // Test sub-agent creation with tool configuration
    console.log('🤖 Testing Sub-Agent Creation with Tools:');
    console.log('-' .repeat(60));

    // Create a test sub-agent
    const testSubAgents = await agent._createDocumentationSubAgents(
      'Create technical documentation for the PMO system',
      'Technical Documentation',
      'Sample base content for testing',
      2
    );

    console.log(`📊 Created ${testSubAgents.length} sub-agents:`);
    testSubAgents.forEach((subAgent, index) => {
      console.log(`\n${index + 1}. ${subAgent.agentName}:`);
      console.log(`   - Specialization: ${subAgent.specialization}`);
      console.log(`   - Assignment: ${subAgent.assignment.substring(0, 100)}...`);
      console.log(`   - Complexity: ${subAgent.estimatedComplexity}`);
      console.log(`   - Priority: ${subAgent.priority}`);
      
      if (subAgent.enabledTools) {
        const hasTools = agent._hasAnyToolEnabled(subAgent.enabledTools);
        console.log(`   - Tools Enabled: ${hasTools ? '✅ YES' : '❌ NO'}`);
        if (hasTools) {
          console.log(`     • Chart Generation: ${subAgent.enabledTools.chartGeneration ? '✅' : '❌'}`);
          console.log(`     • Dashboard Creation: ${subAgent.enabledTools.dashboardCreation ? '✅' : '❌'}`);
          console.log(`     • Web Search: ${subAgent.enabledTools.webSearch ? '✅' : '❌'}`);
          console.log(`     • DateTime: ${subAgent.enabledTools.datetime ? '✅' : '❌'}`);
          console.log(`     • Question Answer: ${subAgent.enabledTools.questionAnswer ? '✅' : '❌'}`);
        }
      } else {
        console.log(`   - Tools Enabled: ❌ NO (enabledTools is null/undefined)`);
      }
      
      console.log(`   - Tool Guidance: ${subAgent.toolUseGuidance ? 'Provided' : 'Missing'}`);
    });

    // Test enhanced processor availability
    console.log('\n🔧 Enhanced Processor Verification:');
    console.log('-' .repeat(60));
    
    if (agent.enhancedProcessor) {
      console.log('✅ Enhanced Processor is available');
      console.log('   - This means sub-agents can use function calling');
      console.log('   - Tools will be properly formatted for Anthropic API');
      console.log('   - Sub-agents will execute with _executeSubAgentWithEnhancedProcessor');
    } else {
      console.log('❌ Enhanced Processor is NOT available');
      console.log('   - Sub-agents will fall back to direct execution');
      console.log('   - No function calling capabilities');
      console.log('   - Tools will not be accessible');
    }

    // Test execution path determination
    console.log('\n🚀 Execution Path Testing:');
    console.log('-' .repeat(60));
    
    testSubAgents.forEach((subAgent, index) => {
      const hasTools = subAgent.enabledTools && agent._hasAnyToolEnabled(subAgent.enabledTools);
      const willUseEnhanced = agent.enhancedProcessor && hasTools;
      
      console.log(`${index + 1}. ${subAgent.agentName}:`);
      console.log(`   - Has Enhanced Processor: ${agent.enhancedProcessor ? '✅' : '❌'}`);
      console.log(`   - Has Enabled Tools: ${hasTools ? '✅' : '❌'}`);
      console.log(`   - Will Use Enhanced Execution: ${willUseEnhanced ? '✅ YES' : '❌ NO'}`);
      console.log(`   - Execution Method: ${willUseEnhanced ? '_executeSubAgentWithEnhancedProcessor' : '_executeSubAgentDirectly'}`);
    });

    console.log('\n🎯 Summary:');
    console.log('=' .repeat(60));
    
    const toolConfiguredAgents = testSubAgents.filter(agent => 
      agent.enabledTools && agent._hasAnyToolEnabled && agent._hasAnyToolEnabled(agent.enabledTools)
    ).length;
    
    const enhancedExecutionAgents = testSubAgents.filter(agent => 
      agent.enhancedProcessor && agent.enabledTools && agent._hasAnyToolEnabled && agent._hasAnyToolEnabled(agent.enabledTools)
    ).length;

    console.log(`📊 Total Sub-Agents Created: ${testSubAgents.length}`);
    console.log(`🛠️  Sub-Agents with Tools: ${toolConfiguredAgents}/${testSubAgents.length}`);
    console.log(`🚀 Sub-Agents using Enhanced Execution: ${agent.enhancedProcessor ? testSubAgents.length : 0}/${testSubAgents.length}`);
    console.log(`✅ Function Calling Available: ${agent.enhancedProcessor ? 'YES' : 'NO'}`);
    
    if (agent.enhancedProcessor && toolConfiguredAgents > 0) {
      console.log('\n🎉 VERIFICATION PASSED: Sub-agents are properly configured with tools and function calling!');
    } else {
      console.log('\n⚠️  VERIFICATION ISSUES DETECTED: Check the configuration above');
    }

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run verification if this file is executed directly
if (require.main === module) {
  verifySubAgentToolConfiguration()
    .then(() => {
      console.log('\n🏁 Verification completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Verification failed:', error);
      process.exit(1);
    });
}

module.exports = { verifySubAgentToolConfiguration };
